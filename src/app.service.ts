import { Injectable, NotFoundException } from '@nestjs/common';
import { DeliverableRepository } from '@ghq-abi/northstar-domain';
import { GetDeliverableResponseDto } from './dtos/responses/get-deliverable.dto';
import { GetDeliverablesResponseDto } from './dtos/responses/get-deliverables.dto';
import { CreateDeliverableRequestDto } from './dtos/requests/create-deliverable.dto';
import { UpdateDeliverableRequestDto } from './dtos/requests/update-deliverable.dto';

@Injectable()
export class AppService {
  constructor(private readonly deliverableRepository: DeliverableRepository) { }

  getHealthCheck(): string {
    return 'OK';
  }

  async createDeliverableEntity(
    deliverableDto: CreateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {

    let deliverablesEntities = [];

    if (deliverableDto.deliverableUids && deliverableDto.deliverableUids.length > 0) {
      deliverablesEntities = await this.deliverableRepository.findByUids(
        deliverableDto.deliverableUids,
      );

      const foundUids = deliverablesEntities.map((d) => d.uid);
      const missingUids = deliverableDto.deliverableUids.filter(
        (uid) => !foundUids.includes(uid),
      );

      if (missingUids.length > 0) {
        throw new NotFoundException(
          `Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`,
        );
      }
    }

    const entity = this.deliverableRepository.repository.create({
      buLevelAggregation: deliverableDto.buLevelAggregation,
      businessFunction: deliverableDto.businessFunction,
      calculationMethod: deliverableDto.calculationMethod,
      dateEnd: deliverableDto.dateEnd,
      dateStart: deliverableDto.dateStart,
      definition: deliverableDto.definition,
      frequency: deliverableDto.frequency,
      isActive: deliverableDto.isActive,
      name: deliverableDto.name,
      paValue: deliverableDto.paValue,
      deliverables: deliverablesEntities,
      itemType: 'Deliverable' as const,
      createdAt: new Date(),
      createdBy: sessionUserUuid,
    });

    const savedEntity = await this.deliverableRepository.repository.save(entity);
    return this.mapEntityToResponse(savedEntity);
  }

  async updateDeliverableEntity(
    uid: string,
    deliverableDto: UpdateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    let deliverablesEntities = deliverable.deliverables;

    if (deliverableDto.deliverableUids !== undefined) {
      if (deliverableDto.deliverableUids.length > 0) {
        deliverablesEntities = await this.deliverableRepository.findByUids(
          deliverableDto.deliverableUids,
        );

        const foundUids = deliverablesEntities.map((d) => d.uid);
        const missingUids = deliverableDto.deliverableUids.filter(
          (uid) => !foundUids.includes(uid),
        );

        if (missingUids.length > 0) {
          throw new NotFoundException(
            `Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`,
          );
        }
      } else {
        deliverablesEntities = [];
      }
    }

    Object.assign(deliverable, {
      ...deliverableDto,
      deliverables: deliverablesEntities,
      updatedAt: new Date(),
      updatedBy: sessionUserUuid,
    });

    const savedEntity = await this.deliverableRepository.repository.save(deliverable);
    return this.mapEntityToResponse(savedEntity);
  }

  async getDeliverableEntity(uid: string): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.deliverableRepository.repository.findOne({
      where: { uid },
      relations: ['deliverableType', 'deliverables'],
    });

    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    return this.mapEntityToResponse(deliverable);
  }

  async getAllDeliverableEntities(sessionUserUuid: string): Promise<GetDeliverablesResponseDto> {
    console.log(sessionUserUuid);

    const entities = await this.deliverableRepository.repository.find({
      relations: ['deliverableType', 'deliverables'],
      order: { name: 'ASC' },
    });

    return {
      data: entities.map(entity => this.mapEntityToResponse(entity)),
      pageNumber: 1,
      pageSize: entities.length,
      totalRecords: entities.length,
    };
  }

  // Legacy methods - keeping for backward compatibility if needed
  async createDeliverable(
    deliverableDto: CreateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    // Delegate to the new method
    return this.createDeliverableEntity(deliverableDto, sessionUserUuid);
  }

  async updateDeliverable(
    uid: string,
    deliverableDto: UpdateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    // Delegate to the new method
    return this.updateDeliverableEntity(uid, deliverableDto, sessionUserUuid);
  }

  async softDeleteDeliverable(
    uid: string,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.deliverableRepository.findByUid(uid);

    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    // Get the deliverable data before deletion
    const deliverableResponse = this.mapEntityToResponse(deliverable);

    await this.deliverableRepository.delete(uid, sessionUserUuid);

    return deliverableResponse;
  }

  async getDeliverablesFunctions(): Promise<string[]> {
    const response = await this.deliverableRepository.getFunctions();

    if (!response || response.length === 0) {
      return [];
    }

    return response.map((item) => item.function);
  }

  async getAll(sessionUserUuid: string): Promise<GetDeliverablesResponseDto> {
    // Delegate to the new method
    return this.getAllDeliverableEntities(sessionUserUuid);
  }

  // Helper method to transform entity to response DTO
  private mapEntityToResponse(entity: any): GetDeliverableResponseDto {
    return {
      uid: entity.uid,
      name: entity.name,
      businessFunction: entity.businessFunction,
      frequency: entity.frequency,
      isActive: entity.isActive,
      calculationMethod: entity.calculationMethod,
      definition: entity.definition,
      paValue: entity.paValue,
      dateStart: entity.dateStart,
      dateEnd: entity.dateEnd,
      itemType: 'Deliverable' as const,
      buLevelAggregation: entity.buLevelAggregation,
      deliverableType: entity.deliverableType ? {
        code: entity.deliverableType.code,
        name: entity.deliverableType.name,
      } : undefined,
      deliverables: entity.deliverables ? entity.deliverables.map((d: any) => this.mapEntityToResponse(d)) : [],
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      createdBy: entity.createdBy,
      updatedBy: entity.updatedBy,
    };
  }
}
