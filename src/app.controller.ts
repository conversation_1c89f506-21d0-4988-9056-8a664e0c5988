import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { Public, User, UserSession } from '@ghq-abi/northstar-api-libs';
import { AppService } from './app.service';
import { GetDeliverableResponseDto } from './dtos/responses/get-deliverable.dto';
import { GetDeliverablesResponseDto } from './dtos/responses/get-deliverables.dto';
import { CreateDeliverableRequestDto } from './dtos/requests/create-deliverable.dto';
import { UpdateDeliverableRequestDto } from './dtos/requests/update-deliverable.dto';

@ApiTags('API')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get('health-check')
  @Public()
  @ApiOperation({
    summary: 'Health Check',
    description: 'Returns the health status of the KPI Catalog API service',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
    schema: {
      type: 'string',
      example: 'OK',
    },
  })
  getHealthCheck(): string {
    return this.appService.getHealthCheck();
  }

  @Get('deliverables')
  @ApiOperation({
    summary: 'Get All Deliverables',
    description:
      'Retrieves a complete list of all deliverables in the catalog with full details including metadata, owners, and relationships',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all deliverables',
    type: GetDeliverablesResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getAllDeliverables(@User() user: UserSession): Promise<GetDeliverablesResponseDto> {
    return this.appService.getAllDeliverableEntities(user?.uuid);
  }

  @Get('deliverables/functions')
  @ApiOperation({
    summary: 'Get Deliverable Functions',
    description:
      'Retrieves a list of all unique function values used by deliverables in the catalog',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved deliverable functions',
    schema: {
      type: 'array',
      items: {
        type: 'string',
      },
      example: ['Finance', 'Marketing', 'Operations', 'Sales'],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getDeliverablesFunctions(): Promise<string[]> {
    return this.appService.getDeliverablesFunctions();
  }

  @Get('deliverables/:uid')
  @ApiOperation({
    summary: 'Get Deliverable by UID',
    description: 'Retrieves a specific deliverable by its unique identifier',
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to retrieve',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved deliverable',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getDeliverableByUid(@Param('uid') uid: string): Promise<GetDeliverableResponseDto> {
    return this.appService.getDeliverableEntity(uid);
  }

  @Post('deliverables')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create Deliverable',
    description:
      'Creates a new deliverable in the catalog with the provided details',
  })
  @ApiBody({
    type: CreateDeliverableRequestDto,
    description: 'Deliverable creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'Deliverable successfully created',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async createDeliverable(
    @Body() deliverableDto: CreateDeliverableRequestDto,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponseDto> {
    return this.appService.createDeliverableEntity(deliverableDto, user?.uuid);
  }

  @Put('deliverables/:uid')
  @ApiOperation({
    summary: 'Update Deliverable',
    description: 'Updates an existing deliverable with the provided data',
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to update',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: UpdateDeliverableRequestDto,
    description: 'Deliverable update data',
  })
  @ApiResponse({
    status: 200,
    description: 'Deliverable successfully updated',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async updateDeliverable(
    @Param('uid') uid: string,
    @Body() deliverableDto: UpdateDeliverableRequestDto,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponseDto> {
    return this.appService.updateDeliverableEntity(uid, deliverableDto, user?.uuid);
  }

  @Delete('deliverables/:uid')
  @ApiOperation({
    summary: 'Soft Delete Deliverable',
    description:
      'Performs a soft delete on a deliverable (marks as deleted without removing from database)',
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to delete',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Deliverable successfully soft deleted',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found',
  })
  @ApiResponse({
    status: 501,
    description: 'Not implemented yet',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async softDeleteDeliverable(
    @Param('uid') uid: string,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponseDto> {
    return this.appService.softDeleteDeliverable(uid, user?.uuid);
  }
}
