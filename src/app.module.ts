import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExtraLogMiddleware } from '@ghq-abi/northstar-api-libs';
import { DataSourceOptions } from 'typeorm';
import {
  DeliverableEntity,
  DeliverableRepository,
  DeliverableTypeRepository,
  DeliverableTypeEntity,
} from '@ghq-abi/northstar-domain';
import { typeorm } from './configs/typeorm.config';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (
        configService: ConfigService,
      ): Promise<DataSourceOptions> =>
        configService.get<DataSourceOptions>('typeorm'),
    }),
    TypeOrmModule.forFeature([
      DeliverableEntity,
      DeliverableTypeEntity,
    ]),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    ExtraLogMiddleware,
    DeliverableRepository,
    DeliverableTypeRepository,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ExtraLogMiddleware).exclude({
      method: RequestMethod.GET,
      path: '*',
    });
  }
}
